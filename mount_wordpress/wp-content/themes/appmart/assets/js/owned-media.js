/**
 * オウンドメディアLP専用JavaScript
 */

(function () {
  'use strict';

  // DOM読み込み完了後に実行
  document.addEventListener('DOMContentLoaded', function () {
    // プログレスマーク初期化
    initProgressMarks();

    // スマホ版での提携企業ロゴ無限スクロール初期化
    initPartnerLogosScroll();

    // スムーススクロール初期化
    initSmoothScroll();

    // FAQアコーディオン初期化（将来実装用）
    initFAQAccordion();
  });

  /**
   * プログレスマーク初期化
   */
  function initProgressMarks() {
    const serviceItems = document.querySelectorAll(
      '.owned-media-service__item-number[data-progress]',
    );

    serviceItems.forEach(function (item) {
      const progressValue = parseInt(item.getAttribute('data-progress'));
      updateProgressMark(item, progressValue);
    });
  }

  /**
   * プログレスマークを更新
   * @param {Element} element - 番号要素
   * @param {number} activeStep - アクティブなステップ（1-7）
   */
  function updateProgressMark(element, activeStep) {
    // 7個のドットの背景画像を生成
    const dots = [];
    const backgroundLine = 'linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%)';

    for (let i = 1; i <= 7; i++) {
      if (i === activeStep) {
        // アクティブなドット：白い塗りつぶし + 緑の外周線
        dots.push(
          'radial-gradient(circle, #7dc8b6 11px, #ffffff 11px, #ffffff 8px, transparent 8px)',
        );
      } else {
        // 非アクティブなドット：グレーの塗りつぶし
        dots.push('radial-gradient(circle, #d9d9d9 11px, transparent 11px)');
      }
    }

    // ドットを前面に、背景ラインを後面に配置
    const backgroundImages = [...dots, backgroundLine];

    // 背景位置を設定（ドット7個 + 背景ライン1個）
    const backgroundPositions = [
      '0 0', // ドット1
      '28px 0', // ドット2
      '57px 0', // ドット3
      '86px 0', // ドット4
      '115px 0', // ドット5
      '143px 0', // ドット6
      '172px 0', // ドット7
      '11px 10px', // 背景ライン
    ];

    // 背景サイズを設定
    const backgroundSizes = [
      '22px 22px', // ドット1
      '22px 22px', // ドット2
      '22px 22px', // ドット3
      '22px 22px', // ドット4
      '22px 22px', // ドット5
      '22px 22px', // ドット6
      '22px 22px', // ドット7
      '172px 3px', // 背景ライン
    ];

    // 擬似要素のスタイルを動的に設定
    const style = document.createElement('style');
    const uniqueClass = 'progress-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    element.classList.add(uniqueClass);

    style.textContent = `
      .${uniqueClass}::before {
        background-image: ${backgroundImages.join(', ')};
        background-position: ${backgroundPositions.join(', ')};
        background-size: ${backgroundSizes.join(', ')};
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 提携企業ロゴの無限スクロール初期化
   */
  function initPartnerLogosScroll() {
    const logoSection = document.querySelector('.owned-media-partner-logos');
    const scrollingTracks = document.querySelectorAll(
      '.owned-media-partner-logos__scrolling-track',
    );

    if (!logoSection || scrollingTracks.length === 0) return;

    // 各スクロールトラックに対してロゴを複製
    scrollingTracks.forEach(function (track) {
      const originalLogos = Array.from(track.children);

      // 無限スクロール用にロゴを複製（3回繰り返し）
      for (let i = 0; i < 2; i++) {
        originalLogos.forEach(function (logo) {
          const clonedLogo = logo.cloneNode(true);
          track.appendChild(clonedLogo);
        });
      }
    });

    // リサイズ時の処理
    window.addEventListener(
      'resize',
      debounce(function () {
        // リサイズ時は特に処理しない（CSSで制御）
      }, 250),
    );
  }

  /**
   * スムーススクロール初期化
   */
  function initSmoothScroll() {
    const ctaButtons = document.querySelectorAll(
      '.owned-media__cta-form-wrapper, .owned-media__fv-form-wrapper',
    );

    ctaButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        // 実際のフォームが実装されるまでは、お問い合わせセクションへスクロール
        const contactSection = document.querySelector('.section_contact');
        if (contactSection) {
          e.preventDefault();
          contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      });
    });
  }

  /**
   * FAQアコーディオン初期化（将来実装用）
   */
  function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.owned-media__faq-item');

    faqItems.forEach(function (item) {
      const question = item.querySelector('.owned-media__faq-question');
      const answer = item.querySelector('.owned-media__faq-answer');

      if (question && answer) {
        question.addEventListener('click', function () {
          const isOpen = item.classList.contains('is-open');

          // 他のFAQを閉じる
          faqItems.forEach(function (otherItem) {
            otherItem.classList.remove('is-open');
            const otherAnswer = otherItem.querySelector('.owned-media__faq-answer');
            if (otherAnswer) {
              otherAnswer.style.maxHeight = '0';
            }
          });

          // クリックされたFAQの開閉
          if (!isOpen) {
            item.classList.add('is-open');
            answer.style.maxHeight = answer.scrollHeight + 'px';
          }
        });
      }
    });
  }

  /**
   * パフォーマンス最適化：debounce関数
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = function () {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 要素が画面に表示されたときのアニメーション（将来実装用）
   */
  function initScrollAnimations() {
    const animationElements = document.querySelectorAll(
      '.owned-media__services-item, .owned-media__merits-item, .owned-media__support-image-step',
    );

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px',
    };

    const observer = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    animationElements.forEach(function (element) {
      observer.observe(element);
    });
  }

  /**
   * モバイルデバイス検出
   */
  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
  }

  /**
   * タッチデバイス用の最適化
   */
  function initTouchOptimizations() {
    if (isMobileDevice()) {
      // タッチデバイス用のクラスを追加
      document.documentElement.classList.add('touch-device');

      // iOS Safariでの100vh問題を解決
      const setVH = function () {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', vh + 'px');
      };

      setVH();
      window.addEventListener('resize', debounce(setVH, 250));
    }
  }

  // タッチ最適化を初期化
  initTouchOptimizations();
})();
