// ==================================================
// オウンドメディアLP専用スタイル
// ==================================================

// ==================================================
// グローバル設定
// ==================================================
.owned-media-lp * {
  box-sizing: border-box;
}

// ==================================================
// 変数定義
// ==================================================
$owned-media-primary-color: #fa6b58;
$owned-media-bg-color: #f9f9f9;
$owned-media-text-color: #333;
$owned-media-text-color-base: #333;
$owned-media-text-color-sumikuro: #5f6061;
$owned-media-white: #fff;
$owned-media-base-color: #f9f9f9;
$owned-media-mint: #3ab795;
$owned-media-mint-light: #b1e2d5;

// フォント設定
// Noto Sans JP
$owned-media-font-family-noto: 'Noto Sans JP', helvetica, sans-serif;

// Yu Gothic
$owned-media-font-family-yu:
  'Yu Gothic', 'YuGothic', 'Hiragino Kaku Gothic ProN', 'Hiragino Sans', meiryo, sans-serif;

// ブレークポイント
$owned-media-breakpoint-lg: 1000px;
$owned-media-breakpoint-md: 768px;
$owned-media-breakpoint-sm: 480px;

// 画像パス
$owned-media-image-path: '/wp-content/themes/appmart/assets/images/s-owned';

// ==================================================
// mixint定義
// ==================================================

// セクション
@mixin owned-media-section($bg-color: #fff) {
  width: 100%;
  padding: 180px 0 100px;
  background-color: $bg-color;
}

// セクション見出し
@mixin section-header {
  &__header {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: center;
    margin-bottom: 80px;
    text-align: center;
  }

  &__title {
    font-family: $owned-media-font-family-noto;
    font-size: clamp(52px, 10vw, 80px);
    font-weight: 700;
    color: #3ab795;
  }

  &__subtitle {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: center;

    &::before,
    &::after {
      display: inline-block;
      width: 15px;
      height: 4px;
      content: '';
      background-color: #3ab795;
      border-radius: 2px;
    }

    &::before {
      margin-right: 8px;
    }

    &::after {
      margin-left: 8px;
    }
  }

  &__subtitle-text {
    font-family: $owned-media-font-family-noto;
    font-size: 38px;
    font-weight: 500;
    line-height: 1;
    color: #3ab795;
    letter-spacing: 0.38px;
  }
}

// セクションコンテナ
@mixin owned-media-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1332px; // 1300px + padding(16px × 2)
  padding: 0 16px;
  margin: 0 auto;
}

// レスポンシブブレークポイント用mixin
@mixin media-breakpoint-down($breakpoint) {
  @if $breakpoint == 'lg' {
    @media (max-width: #{$owned-media-breakpoint-lg}) {
      @content;
    }
  } @else if $breakpoint == 'md' {
    @media (max-width: #{$owned-media-breakpoint-md}) {
      @content;
    }
  } @else if $breakpoint == 'sm' {
    @media (max-width: #{$owned-media-breakpoint-sm}) {
      @content;
    }
  }
}

// モバイル用mixin
@mixin owned-media-mobile {
  @media (max-width: #{$owned-media-breakpoint-md}) {
    @content;
  }
}

// PC/SP表示切り替え用クラス
.owned-media-pc-only {
  @include media-breakpoint-down(md) {
    display: none !important;
  }
}

.owned-media-sp-only {
  display: none !important;

  @include media-breakpoint-down(md) {
    display: block !important;
  }
}

// ==================================================
// メインラッパー
// ==================================================
.owned-media-lp {
  width: 100%;
  overflow-x: hidden;
}

// ========================================
// CTAボタンセクション
// ========================================
.owned-media-cta {
  position: relative;
  width: 100%;
  background-color: $owned-media-base-color;

  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 80px 0;
  }

  &__title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: 38px;
    font-weight: 700;
    line-height: 46px;
    text-align: center;
    white-space: nowrap;

    &-prefix,
    &-suffix {
      color: $owned-media-text-color-base;
      letter-spacing: -0.43px;
    }

    &-accent {
      color: $owned-media-primary-color;
      letter-spacing: -0.43px;
    }
  }

  &__buttons {
    display: flex;
    gap: 42px;
    margin-top: 80px;
  }

  &__button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    max-width: 620px;
    height: 160px;
    padding: 24px 56px 24px 32px;
    text-decoration: none;
    border-radius: 160px;
    box-shadow: 0 9px 19px rgb(82 134 120 / 13%);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 12px 24px rgb(82 134 120 / 20%);
      transform: translateY(-3px);
    }

    &--outline {
      background-color: $owned-media-base-color;
      border: 9px solid $owned-media-primary-color;
      transition: all 0.3s ease;

      .owned-media-cta__button-text {
        font-size: clamp(24px, 3vw, 38px);
        color: $owned-media-primary-color;
        white-space: nowrap;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: $owned-media-primary-color;
        border-color: $owned-media-primary-color;

        .owned-media-cta__button-text {
          color: $owned-media-white;
        }

        .owned-media-cta__button-arrow path {
          stroke: $owned-media-white;
        }
      }
    }

    &--filled {
      background-color: $owned-media-primary-color;
      border: 9px solid $owned-media-primary-color;
      transition: all 0.3s ease;

      .owned-media-cta__button-text {
        font-size: clamp(24px, 3vw, 38px);
        font-weight: 700;
        color: $owned-media-white;
        white-space: nowrap;
        transition: color 0.3s ease;
      }

      &:hover {
        background-color: $owned-media-base-color;
        border-color: $owned-media-primary-color;

        .owned-media-cta__button-text {
          color: $owned-media-primary-color;
        }

        .owned-media-cta__button-arrow path {
          stroke: $owned-media-primary-color;
        }
      }
    }

    &-text {
      font-family: $owned-media-font-family-noto;
      font-size: 38px;
      font-weight: 500;
      line-height: 59px;
      text-align: center;
      letter-spacing: 0.38px;
    }
  }

  // レスポンシブ対応
  @include media-breakpoint-down(md) {
    height: 482px;

    &__container {
      padding: 0 16px;
    }

    &__title {
      top: 0;
      font-size: 32px;
      line-height: 40px;

      &::after {
        width: 323px;
        height: 30px;
      }
    }

    &__buttons {
      flex-direction: column;
      gap: 31px;
      width: 100%;
      margin-top: 94px;
    }

    &__button {
      width: 100%;
      max-width: 343px;
      height: 140px;
      padding: 0 30px;
      border-radius: 160px;

      &--outline {
        border-width: 7px;
      }

      &-text {
        font-size: 24px;
        line-height: 40px;
        letter-spacing: 0.24px;
      }

      &-arrow {
        right: 30px;
        width: 15px;
        height: 24px;
      }
    }
  }
}

// ==================================================
// FVセクション
// ==================================================
.owned-media-fv {
  position: relative;
  width: 100%;
  min-height: 1024px;
  overflow: hidden;

  // Figma正確な背景グラデーション
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 40%) 0%,
    rgb(227 243 255 / 40%) 33.17%,
    rgb(129 204 231 / 40%) 64.42%
  );

  // コンテナ - Figmaの正確な配置
  &__container {
    @include owned-media-container;

    position: relative;
    display: grid;
    grid-template-columns: 1fr 375px; // Figmaのフォーム幅375px
    gap: 65px; // 1440px - 762px(content) - 375px(form) = 303px / 約65px
    align-items: start;
    height: 100%;
    min-height: 1024px;
    padding-top: 53px; // Figmaのform位置Y座標
  }

  // メインイラストレーション - Figma位置
  &__main-illustration {
    position: absolute;
    top: 202.41px; // Figma正確値
    left: 0;
    z-index: 1;
    width: 1929px; // Figma幅
    height: 821.59px; // Figma高さ
    pointer-events: none;
    object-fit: contain;
  }

  // 左側コンテンツエリア - Figma位置とサイズ
  &__content {
    position: relative;
    z-index: 2;
    width: 762.12px; // Figma幅
    height: 742.86px; // Figma高さ
    margin-top: 29.63px; // Figma Y座標
    margin-left: 239.42px; // Figma X座標からコンテナ左端までの距離
  }

  // メインタイトルエリア - 簡素化されたレイアウト
  &__title-area {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
    margin-top: 40px;
    margin-bottom: 30px;
  }

  // 上部ターゲットテキスト - Figma Group 11
  &__target-text {
    position: absolute;
    top: -140.94px; // title-area上部に配置
    left: -18.27px; // Group 11のX座標調整
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 665.07px; // Figma Group 11の幅
    height: 166.93px; // Figma Group 11の高さ
    font-family: $owned-media-font-family-noto;
    font-size: 34px; // Figma style_RA1TNJ
    font-weight: 700;
    line-height: 1.2;
    color: #19191a; // Figma fill_8SFRVL
    letter-spacing: 0.01em; // 1%

    span {
      &:first-child {
        margin-top: 15.79px; // 7.97 + 7.82の調整
      }

      &:last-child {
        margin-top: 88.06px; // 80.24 + 7.82の調整
      }
    }
  }

  // タイトルコンテナ - 水平配置
  &__title-container {
    display: flex;
    gap: 0;
    align-items: center;

    // オウンドメディアバッジ
    .owned-media-fv__title-badge {
      display: inline-block;
      padding: 15px 30px;
      background-color: #333;
      border-radius: 40px;

      // オウンドメディアテキスト
      h1 {
        margin: 0;
        font-family: $owned-media-font-family-noto;
        font-size: 50px;
        font-weight: 900;
        line-height: 1.2;
        color: #fff;
        letter-spacing: -0.06em;
        white-space: nowrap;
      }
    }
  }

  // 「の運用」部分の白背景
  &__background-shape {
    display: inline-block;
    padding: 10px 20px;
    margin-left: 10px;
    background-color: #fff;
    border-radius: 40px;

    .title-text {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 50px;
      font-weight: 900;
      line-height: 1.2;
      color: #333;
      text-align: center;
      white-space: nowrap;
    }
  }

  // 「まるごと代行します」エリア
  &__title-sub {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    margin-top: 30px;

    // 「代行します」テキスト
    .owned-media-fv__subtitle {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 72px;
      font-weight: 900;
      line-height: 1.2;
      color: #333;
    }
  }

  // 「まる」「ごと」の強調テキスト
  &__emphasis {
    display: inline-block;
    padding: 0;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-weight: 900;
    line-height: 1;

    &--maru {
      font-size: 120px; // 大きなサイズ
      color: #fa6b58; // オレンジ色
      letter-spacing: -0.17em;
    }

    &--goto {
      font-size: 120px; // 大きなサイズ
      color: #fa6b58; // オレンジ色
      letter-spacing: -0.13em;
    }
  }

  // ターゲット説明エリア
  &__target-description {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__target-group {
    display: flex;
    gap: 20px;
    align-items: center;
    font-family: $owned-media-font-family-noto;
    font-size: clamp(18px, 2.5vw, 24px);
    color: $owned-media-text-color;
  }

  &__target-dots {
    display: flex;
    gap: 5px;
  }

  &__dot {
    width: 12px;
    height: 12px;
    background-color: $owned-media-primary-color;
    border-radius: 50%;
  }

  &__decorative-line {
    &--1 {
      display: none; // 装飾ライン非表示
    }

    &--2 {
      display: none; // 装飾ライン非表示
    }
  }

  // 右側フォームエリア - Figma位置とサイズ
  &__form-area {
    position: relative;
    z-index: 2;
    align-self: start;
    width: 375px; // Figma幅
    height: 618px; // Figma高さ
    padding: 40px 30px;
    background: rgb(255 255 255 / 90%);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
  }

  // フォームプレースホルダー
  &__form-placeholder {
    text-align: center;

    h3 {
      margin: 0 0 20px;
      font-family: $owned-media-font-family-noto;
      font-size: 24px;
      font-weight: 700;
      color: $owned-media-text-color;
    }

    p {
      margin: 0 0 15px;
      font-family: $owned-media-font-family-noto;
      font-size: 16px;
      color: $owned-media-text-color;
    }

    small {
      font-family: $owned-media-font-family-noto;
      font-size: 12px;
      color: #666;
    }
  }

  // 不要なスタイル削除
  &__target-description,
  &__target-group,
  &__target-dots,
  &__dot {
    display: none;
  }

  // ==================================================
  // SP版レスポンシブ対応
  // ==================================================
  @include owned-media-mobile {
    min-height: 817px; // Figma SP版高さ

    // コンテナ - SP版レイアウト
    &__container {
      display: block; // グリッドから通常のブロックに変更
      min-height: 817px;
      padding: 0 20px; // Figma SP版パディング
    }

    // メインイラストレーション - SP版調整
    &__main-illustration {
      top: 0;
      left: 50%;
      width: 375px; // SP版幅
      height: 813px; // SP版高さ
      transform: translateX(-50%);
    }

    // コンテンツエリア - SP版配置
    &__content {
      position: relative;
      width: 335px; // Figma SP版コンテンツ幅
      height: 310px; // Figma SP版コンテンツ高さ
      margin: 41px auto 0; // Figma SP版位置（top: 41px, 中央寄せ）
    }

    // ターゲットテキスト - SP版レイアウト
    &__target-text {
      position: absolute;
      top: 0; // SP版では一番上に配置
      left: 1px; // Figma調整
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      width: 334px; // Figma SP版幅
      height: 60px; // Figma SP版高さ
      font-size: 20px; // Figma SP版フォントサイズ
      font-weight: 700;
      color: #19191a;
      letter-spacing: 0.2px; // Figma値

      span {
        &:first-child {
          margin-top: 0; // リセット
          margin-bottom: 9px; // 行間調整
          white-space: nowrap;
        }

        &:last-child {
          margin-top: 0; // リセット
          white-space: nowrap;
        }
      }
    }

    // タイトルエリア - SP版配置
    &__title-area {
      position: absolute;
      top: 80px; // Figma SP版位置
      left: 0;
      width: 339px; // Figma SP版幅
      height: 63px; // Figma SP版高さ
      margin: 0;
    }

    // タイトルコンテナ - SP版レイアウト
    &__title-container {
      position: relative;
      width: 335px; // Figma背景幅
      height: 63px; // Figma背景高さ
      background-image: url('#{$owned-media-image-path}/union.svg');
      background-size: 100% 100%;

      // オウンドメディアバッジ - SP版スタイル
      .owned-media-fv__title-badge {
        position: absolute;
        top: 17px; // Figma位置
        left: 20px; // Figma位置
        width: 203px; // Figma幅
        padding: 0;
        background-color: transparent; // 背景画像を使用
        border-radius: 0;

        h1 {
          font-size: 27px; // Figma SP版フォントサイズ
          font-weight: 900;
          line-height: 20px; // Figma行高
          color: #fff;
          text-align: center;
          letter-spacing: -1.62px; // Figma値
        }
      }
    }

    // 「の運用」部分 - SP版スタイル
    &__background-shape {
      position: absolute;
      top: 0; // Figma位置
      left: 235px; // Figma位置
      width: 99px; // Figma幅
      height: 55px; // Figma高さ
      padding: 0;
      margin: 0;
      background-image: url('#{$owned-media-image-path}/subtract.svg');
      background-size: 100% 100%;
      border-radius: 0;

      .title-text {
        position: absolute;
        top: 15px; // Figma位置調整
        left: 5px; // Figma位置調整
        width: 89px; // Figma幅
        font-size: 27px; // Figma SP版フォントサイズ
        font-weight: 900;
        line-height: 24px; // Figma行高
        color: #333;
        text-align: center;
        letter-spacing: 0; // Figmaデフォルト
      }
    }

    // 「まるごと代行します」エリア - SP版配置
    &__title-sub {
      position: absolute;
      top: 160px; // Figma SP版位置
      left: 19px; // Figma SP版位置
      display: block; // flexから変更
      width: 304px; // Figma SP版幅
      height: 150px; // Figma SP版高さ
      margin: 0;

      // 「代行します」テキスト - SP版スタイル
      .owned-media-fv__subtitle {
        position: absolute;
        top: 90px; // Figma位置
        left: 14px; // Figma位置
        font-size: 56px; // Figma SP版フォントサイズ
        font-weight: 900;
        line-height: 60px; // Figma行高
        text-align: center;
        letter-spacing: 0; // Figmaデフォルト
        white-space: nowrap;
      }
    }

    // 「まる」「ごと」の強調テキスト - SP版スタイル
    &__emphasis {
      position: absolute;

      &--maru {
        top: 0; // Figma位置
        left: 0; // Figma位置
        font-size: 90px; // Figma SP版フォントサイズ
        font-weight: 900;
        line-height: 86px; // Figma行高
        color: #fa6b58;
        letter-spacing: -15.3px; // Figma値
      }

      &--goto {
        top: 4px; // Figma位置
        left: 154px; // Figma位置
        font-size: 79px; // Figma SP版フォントサイズ
        font-weight: 900;
        line-height: 86px; // Figma行高
        color: #333;
        letter-spacing: -10.27px; // Figma値
      }
    }

    // フォームエリア - SP版では非表示
    &__form-area {
      display: none;
    }
  }
}

// ==================================================
// ファースト訴求セクション
// ==================================================
.owned-media-first-appeal {
  @include owned-media-section($owned-media-mint-light);

  width: 100%;
  padding-top: 120px;

  // コンテナ
  &__container {
    @include owned-media-container;

    &::after {
      position: absolute;
      top: -200px;
      left: 50%;
      z-index: 1;
      width: 200px;
      height: 200px;
      content: '';
      background-image: url('#{$owned-media-image-path}/first-appeal-decoration.png');
      background-repeat: no-repeat;
      background-size: contain; /* 背景画像のサイズを指定 */
      transform: translateX(-50%) rotate(5deg);
    }
  }

  // メッセージエリア
  &__message-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    text-align: center;
  }

  // メインメッセージ
  &__main-message {
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    margin-bottom: 100px;
    font-family: $owned-media-font-family-noto;
    font-weight: 900;

    &::after {
      position: absolute;
      bottom: -120px;
      left: 65%;
      z-index: 1;
      width: 440px;
      height: 140px;
      content: '';
      background-image: url('#{$owned-media-image-path}/graffiti-vector.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      transform: translateX(-50%);
    }

    &-from {
      font-size: clamp(50px, 7vw, 100px);
      line-height: 1;
      color: $owned-media-text-color;
      letter-spacing: -3px;
      white-space: nowrap;
      transform: rotate(-5deg);
    }

    &-connector {
      font-size: clamp(40px, 5.6vw, 80px);
      line-height: 1;
      color: $owned-media-text-color;
      letter-spacing: -0.03em;
      white-space: nowrap;
    }

    &-to {
      order: 3;
      font-size: clamp(60px, 8.5vw, 121px);
      line-height: 1;
      color: $owned-media-primary-color;
      letter-spacing: -3.63px;
      white-space: nowrap;
      transform: rotate(-5deg);
    }

    &-suffix {
      position: relative;
      left: -22px;
      order: 4;
      font-size: clamp(40px, 5.6vw, 80px);
      line-height: 1;
      color: $owned-media-text-color;
      letter-spacing: -0.03em;
      white-space: nowrap;
    }
  }

  // サブメッセージ
  &__sub-message {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    text-align: center;
  }

  &__sub-message-text {
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: clamp(24px, 3vw, 38px);
    font-weight: 700;
    line-height: 1.8;
    text-align: center;
    letter-spacing: -0.03em;
  }

  &__sub-message-line1 {
    display: block;
    margin-bottom: 10px;
    color: $owned-media-text-color;
  }

  &__sub-message-line2 {
    display: inline-block;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    height: 60px;
    padding: 12px;
    margin: 0 auto;
    color: $owned-media-white;
    background-color: $owned-media-primary-color;
    border-radius: 4px;
  }

  // 人物画像グループ
  &__people {
    display: flex;
    flex-wrap: wrap;
    gap: clamp(20px, 5vw, 60px);
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    margin-bottom: 40px; // 下部余白を追加
    &-message {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: clamp(30px, 4vw, 78px);
      font-weight: 900;
      line-height: 1.4;
      color: $owned-media-text-color;
      text-align: center;
      letter-spacing: -2.34px;
      white-space: nowrap;
    }
  }

  &__person {
    height: 180px;
    object-fit: contain;
    object-position: bottom;

    &--1 {
      width: clamp(35px, 8vw, 47px);
    }

    &--2 {
      width: clamp(55px, 12vw, 73px);
    }

    &--3 {
      width: clamp(38px, 9vw, 51px);
    }

    &--4 {
      width: clamp(49px, 11vw, 65px);
    }
  }

  // 装飾画像
  &__decoration {
    position: absolute;
    top: 0;
    right: 5%;
    width: clamp(150px, 15vw, 219px);
    height: auto;
    object-fit: contain;
  }

  // レスポンシブ対応
  @include media-breakpoint-down(lg) {
    min-height: 650px;
    padding: 80px 0 30px;

    &__message-area {
      margin-top: 60px;
    }
  }

  @include media-breakpoint-down(md) {
    min-height: 824px;
    padding: 0;
    background: linear-gradient(
      to bottom,
      transparent 0,
      transparent 53px,
      $owned-media-bg-color 53px,
      $owned-media-bg-color 100%
    );

    &__container {
      position: relative;
      width: 100%;
      height: 824px;
    }

    &__message-area {
      position: absolute;
      top: 130px;
      left: 16px;
      gap: 0;
      width: 347px;
      height: 350px;
      margin-top: 0;
    }

    &__main-message {
      position: relative;
      flex-direction: column;
      gap: 0;
      width: 339px;
      height: 219px;
      margin-bottom: 49px;
    }

    &__main-message-from {
      position: absolute;
      top: 7px;
      left: 2px;
      margin: 0;
      font-size: 50px;
      line-height: 43px;
      letter-spacing: -1.5px;
      transform: rotate(-5deg);
    }

    &__main-message-connector {
      position: absolute;
      top: 20px;
      left: 160px;
      margin: 0;
      font-size: 31px;
      line-height: 32px;
      letter-spacing: -0.93px;
      transform: none;
    }

    &__graffiti {
      position: absolute;
      top: 83px;
      left: 15px;
      z-index: 1;
      order: 0;
      width: 319px;
      height: 136px;
      margin: 0;
    }

    &__main-message-to {
      position: absolute;
      top: 65px;
      left: 1px;
      z-index: 2;
      margin: 0;
      font-size: 50px;
      line-height: 43px;
      letter-spacing: -1.5px;
      transform: rotate(-5deg);
    }

    &__main-message-suffix {
      position: absolute;
      top: 71px;
      left: 308px;
      margin: 0;
      font-size: 31px;
      line-height: 32px;
      letter-spacing: -0.93px;
      transform: none;
    }

    &__sub-message {
      position: absolute;
      top: 188px;
      left: 35px;
      width: 277px;
      margin: 0;
    }

    &__sub-message-text {
      margin-bottom: 18px;
      font-size: 20px;
      line-height: 31px;
      letter-spacing: -0.6px;
    }

    &__sub-message-line1 {
      display: block;
      font-weight: 700;
      color: $owned-media-text-color;
    }

    &__sub-message-line2 {
      position: absolute;
      top: 268px;
      left: -35px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 375px;
      height: 82px;
      padding: 5px 20px;
      margin: 0;
      font-size: 20px;
      line-height: 35px;
      text-align: center;
      letter-spacing: -0.6px;
    }

    &__bottom-message {
      position: absolute;
      top: 505px;
      left: 55px;
      margin: 0;
      font-size: 39px;
      line-height: 45px;
      text-align: center;
      letter-spacing: -1.17px;
      white-space: nowrap;
    }

    &__people {
      position: absolute;
      top: 574px;
      left: 53px;
      flex-wrap: nowrap;
      gap: 0;
      justify-content: flex-start;
      width: 249px;
      height: 170px;
    }

    &__person {
      position: absolute;
      height: auto;
      object-position: bottom;

      &--1 {
        top: 6px;
        left: 0;
        width: 43px;
        height: 164px;
      }

      &--2 {
        top: 2px;
        left: 54px;
        width: 67px;
        height: 168px;
        margin: 0;
      }

      &--3 {
        top: 2px;
        left: 132px;
        width: 47px;
        height: 166px;
        margin: 0;
      }

      &--4 {
        top: 0;
        left: 190px;
        width: 59px;
        height: 168px;
      }
    }

    &__decoration {
      position: absolute;
      top: 0;
      right: auto;
      left: 148px;
      width: 104px;
      height: 104px;
    }
  }
}

// ==================================================
// 提携企業ロゴセクション
// ==================================================
.owned-media-partner-logos {
  width: 100%;
  padding: 80px 0; // 60px → 80px に拡大
  overflow: hidden;
  background-color: #f9f9f9;

  // コンテナ
  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  // PC版ロゴグループ
  &__logos-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: center;
    width: 100%;
    min-height: 50px;
  }

  // 各行のロゴ
  &__row {
    display: flex;
    flex-wrap: nowrap;
    gap: 25px;
    align-items: center;
    justify-content: center;
    width: 100%;

    &--1 {
      margin-bottom: 15px;
    }

    &--2 {
      margin-top: 15px;
    }
  }

  // ロゴ画像（PC版）
  &__logo-group {
    width: auto;
    height: 50px;
    margin: 0 10px;
    object-fit: contain;
    mix-blend-mode: multiply;
  }

  // スマホ版無限スクロールコンテナ
  &__scrolling-container {
    display: none; // PC版では非表示
  }

  // スマホ版スクロールトラック
  &__scrolling-track {
    display: flex;
    gap: 20px;
    align-items: center;
    width: max-content;
  }

  // スマホ対応
  @include media-breakpoint-down(md) {
    height: 200px;
    padding: 0;
    overflow: hidden;

    &__container {
      position: relative;
      width: 100%;
      max-width: none;
      height: 200px;
      padding: 0;
      overflow: hidden;
    }

    // PC版を非表示
    &__logos-wrapper {
      display: none;
    }

    // スマホ版を表示
    &__scrolling-container {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 200px;
    }

    &__scrolling-row {
      display: flex;
      align-items: center;
      width: 100%;
      height: 50px;
      overflow: hidden;

      &--top {
        margin-top: 40px;
        margin-bottom: 20px;
      }

      &--bottom {
        margin-top: 0;
        margin-bottom: 40px;
      }
    }

    &__scrolling-track {
      display: flex;
      gap: 20px;
      align-items: center;
      width: max-content;
      animation: scroll-logos 20s linear infinite;
    }

    &__scrolling-logo {
      flex-shrink: 0;
      width: auto;
      max-width: 140px;
      height: 45px;
      margin: 0 10px;
      object-fit: contain;
      mix-blend-mode: multiply;
    }
  }

  // スクロールアニメーション
  @keyframes scroll-logos {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-100%);
    }
  }
}

// ========================================
// 共感パートセクション
// ========================================
.owned-media-empathy {
  position: relative;
  width: 100%;
  background-color: $owned-media-base-color;

  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 130px 0;
  }

  // 見出しエリア
  &__header {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    justify-content: center;
    margin-bottom: 115px;
  }

  &__subtitle {
    margin: 0 auto;
    text-align: center;

    &-text {
      position: relative;
      z-index: 2;
      font-family: $owned-media-font-family-noto;
      font-size: 48px;
      font-weight: 700;
      line-height: 1;
      color: $owned-media-text-color;
      letter-spacing: -1.44px;
    }
  }

  &__title {
    display: inline-block;
    text-align: center;

    &-text {
      position: relative;
      z-index: 2;
      font-family: $owned-media-font-family-noto;
      font-size: clamp(40px, 5vw, 56px);
      font-weight: 700;
      line-height: 1;
      color: $owned-media-text-color;
      letter-spacing: -1.68px;
      white-space: nowrap;

      &::after {
        position: absolute;
        bottom: 8px;
        left: 0;
        z-index: -1;
        width: 100%;
        height: 39px;
        content: '';
        background-color: $owned-media-bg-color;
      }

      @include owned-media-mobile {
        font-size: 56px;
      }
    }
  }

  // チェックリストエリア
  &__checklist {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 80px;
    border: 8px solid #7dc8b6;
    border-radius: 12px;
  }

  &__list {
    display: flex;
    flex-direction: column;
    list-style: none;
  }

  &__item {
    position: relative;
    display: flex;
    align-items: flex-start;
    padding: 48px 0;

    &::before {
      position: absolute;
      bottom: 50%;
      left: 0;
      z-index: 1;
      width: 58px;
      height: 58px;
      content: '';
      border: 7px solid #7dc8b6;
      border-radius: 9px;
      transform: translateY(50%);
    }

    &::after {
      position: absolute;
      bottom: 55%;
      left: 8px;
      z-index: 2;
      width: 83px;
      height: 83px;
      content: '';
      background-image: url('#{$owned-media-image-path}/check-icon.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transform: translateY(50%);
    }

    &-text {
      flex: 1;
      margin: 0;
      margin-left: 96px;
      font-family: $owned-media-font-family-noto;
      font-size: clamp(24px, 3vw, 46px);
      font-weight: 700;
      color: $owned-media-text-color;
      word-wrap: break-word;
      overflow-wrap: break-word;

      &::before {
        position: absolute;
        bottom: 0;
        left: 96px;
        width: 90%;
        height: 1px;
        content: '';
        border-bottom: 6px dashed #c9e9ed;
      }
    }

    &-accent {
      font-weight: 900;
      color: $owned-media-primary-color;
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    height: 1403px;

    &__header {
      position: relative;
      top: 0;
      left: 0;
      max-width: 335px;
      height: 106px;
      padding: 0 20px;
      margin: 0 auto;
      transform: none;
    }

    &__subtitle {
      display: block;
      width: 100%;

      &-text {
        font-size: 24px;
        letter-spacing: -0.72px;
        white-space: normal;
      }

      &-highlight {
        height: 19px;
      }
    }

    &__title {
      margin: 44px 0 0;

      &-text {
        font-size: 28px;
        letter-spacing: -0.84px;
        white-space: normal;
      }

      &-highlight {
        height: 19px;
      }
    }

    &__checklist {
      position: relative;
      top: 141px;
      left: 0;
      max-width: 335px;
      height: 1182px;
      padding: 0 20px;
      margin: 0 auto;
      transform: none;
    }

    &__checklist-bg {
      border-width: 5px;
    }

    &__bg-decoration {
      top: 5px;
      right: 5px;
      width: 80px;
      height: 80px;
    }

    &__list {
      gap: 40px;
      max-width: 100%;
      padding: 60px 20px;
    }

    &__item {
      flex-direction: column;
      gap: 20px;
      padding-bottom: 40px;

      // スマホ版のチェックボックスアイコンサイズ調整
      &::before {
        width: 40px;
        height: 40px;
      }

      &::after {
        left: 5px;
        width: 60px;
        height: 60px;
      }

      &-text {
        font-size: 20px;
        line-height: 1.5;
        letter-spacing: 0.4px;
      }
    }
  }
}

// 発見パートセクション
.owned-media-discovery {
  position: relative;
  width: 100%;
  background-image:
    linear-gradient(to right, rgb(97 106 109 / 15%) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(97 106 109 / 15%) 1px, transparent 1px);
  background-size: 48px 48px;

  // コンテナの対角線背景
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0; // 背景として配置
    width: 100%;
    height: 100%;
    content: '';
    background: linear-gradient(135deg, #a0c1bb 50%, transparent 50%);
  }

  &::after {
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 1; // 背景として配置
    width: 80px;
    height: 80px;
    content: '';
    background-color: $owned-media-base-color;
    transform: translateX(-50%) translateY(-50%) rotate(135deg) skew(20deg, 20deg);
  }

  &__container {
    @include owned-media-container;

    width: 100%;
    max-width: 1400px;
    padding-top: 80px;
  }

  // 見出し
  &__header {
    width: 100%;
    margin: 0 auto;
  }

  &__title {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
  }

  &__title-prefix {
    position: relative;
    font-family: $owned-media-font-family-noto;
    font-size: clamp(40px, 5vw, 60px);
    font-weight: 900;
    line-height: 1.2;
    color: $owned-media-text-color;
    letter-spacing: -1.5px;
  }

  &__title-main {
    position: relative;
    bottom: -32px;
    margin: 0 18px;
    font-family: $owned-media-font-family-noto;
    font-size: clamp(100px, 10vw, 186px);
    font-weight: 900;
    line-height: 1.2;
    color: white;
    letter-spacing: 10px;
    white-space: nowrap;
    transform: rotate(-5deg);
    -webkit-text-stroke: 8px $owned-media-text-color;
    paint-order: stroke;

    &::before {
      position: absolute;
      top: 0;
      right: -110px;
      z-index: 1;
      width: 93px;
      height: 103px;
      content: '';
      background-image: url('#{$owned-media-image-path}/discovery-accent.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transform: rotate(5deg);
    }
  }

  &__title-suffix {
    position: relative;
    margin-left: 20px;
    font-family: $owned-media-font-family-noto;
    font-size: clamp(40px, 5vw, 61px);
    font-weight: 700;
    color: $owned-media-text-color;
    letter-spacing: -1.83px;
  }

  &__accent {
    position: absolute;
    top: -4px;
    right: 250px;
    width: 101px;
    height: 111px;
  }

  // コンテンツエリア
  &__content {
    position: relative;
    width: 100%;
    height: 1430px;
  }

  // イラストと吹き出しのコンテナ
  &__illustration-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  // 中央のイラスト
  &__illustration {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    transform: translate(-50%, -50%);
  }

  &__person {
    position: relative;
    top: -100px;
    width: 211px;
    height: 445px;
    object-fit: contain;
  }

  &__desk {
    position: absolute;
    top: 100px;
    left: 0;
    width: 354px;
    height: 350px;
    object-fit: contain;
  }

  // 失敗理由の吹き出し
  &__reasons {
    position: relative;
    width: 100%;
    height: 100%;
  }

  &__reason {
    position: absolute;

    &--1 {
      top: 0;
      left: 30px;

      .owned-media-discovery__reason-bubble {
        width: 597px;
        height: 602px;

        &::before {
          background-image: url('#{$owned-media-image-path}/discovery-bubble-1.png');
          transform: rotate(-10deg);
        }
      }

      .owned-media-discovery__reason-text {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -80%);
      }
    }

    &--2 {
      top: 9px;
      left: 540px;

      .owned-media-discovery__reason-bubble {
        width: 611px;
        height: 616px;

        &::before {
          background-image: url('#{$owned-media-image-path}/discovery-bubble-1.png');
          transform: rotate(16deg);
        }
      }

      .owned-media-discovery__reason-text {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -80%);
      }
    }

    &--3 {
      top: 430px;
      right: -30px;

      .owned-media-discovery__reason-bubble {
        width: 491px;
        height: 437px;

        &::before {
          background-image: url('#{$owned-media-image-path}/discovery-bubble-2.png');
        }
      }

      .owned-media-discovery__reason-text {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -80%);
      }
    }

    &--4 {
      right: 0;
      bottom: 243px;

      .owned-media-discovery__reason-bubble {
        width: 506px;
        height: 458px;

        &::before {
          background-image: url('#{$owned-media-image-path}/discovery-bubble-3.png');
        }
      }

      .owned-media-discovery__reason-text {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    &--5 {
      bottom: 0;
      left: 406px;

      .owned-media-discovery__reason-bubble {
        width: 514px;
        height: 522px;

        &::before {
          background-image: url('#{$owned-media-image-path}/discovery-bubble-4.png');
        }
      }

      .owned-media-discovery__reason-text {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -35%);
      }
    }

    &--6 {
      top: 527px;
      left: 0;

      .owned-media-discovery__reason-bubble {
        width: 610px;
        height: 612px;

        &::before {
          background-image: url('#{$owned-media-image-path}/discovery-bubble-1.png');
          transform: rotate(-160deg);
        }
      }

      .owned-media-discovery__reason-text {
        top: 50%;
        left: 50%;
        transform: translate(-55%, -35%);
      }
    }
  }

  &__reason-bubble {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      width: 100%;
      height: 100%;
      content: '';
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }
  }

  &__reason-text {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2; // 背景画像より確実に前面に表示
    width: 80%; // コンテナ幅の80%を使用
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: 38px;
    line-height: 1.16;
    text-align: center;
    transform: translate(-50%, -50%); // 中央配置
  }

  &__reason-strong {
    font-weight: 900;
    color: #3c8b86;
  }

  &__reason-weak {
    font-size: 32px;
    font-weight: 700;
    color: #5f6061;
  }

  // レスポンシブ対応
  @media (width <= 768px) {
    height: auto;
    min-height: 1200px;
    padding: 60px 0;

    &__container {
      padding: 0 20px;
    }

    &__bg-image {
      top: 50px;
      height: auto;
    }

    &__arrow {
      top: 20px;
      width: 120px;
      height: 56px;
    }

    &__header {
      max-width: 100%;
      padding-top: 80px;
    }

    &__title {
      position: relative;
    }

    &__title-prefix {
      display: block;
      margin-bottom: 20px;
      font-size: 28px;
      letter-spacing: -0.84px;
    }

    &__title-main {
      position: static;
      display: block;
      margin: 20px 0;
      font-size: 72px;
      letter-spacing: 7.92px;
      transform: none;
      -webkit-text-stroke: 4px $owned-media-text-color;
      text-stroke: 4px $owned-media-text-color;
    }

    &__title-suffix {
      display: block;
      margin-left: 0;
      font-size: 32px;
      letter-spacing: -0.96px;
    }

    &__accent {
      position: static;
      display: inline-block;
      width: 50px;
      height: 56px;
      margin-left: 10px;
      vertical-align: middle;
    }

    &__content {
      position: static;
      max-width: 100%;
      height: auto;
      margin-top: 60px;
    }

    &__illustration-container {
      position: static;
      height: auto;
    }

    &__illustration {
      position: static;
      margin-bottom: 40px;
      text-align: center;
      transform: none;
    }

    &__person {
      width: 120px;
      height: 253px;
    }

    &__desk {
      position: static;
      width: 200px;
      height: 198px;
      margin-top: -50px;
    }

    &__reasons {
      position: static;
      display: grid;
      grid-template-columns: 1fr;
      gap: 30px;
      padding: 0 10px;
    }

    &__reason {
      position: static !important;
    }

    &__reason-bubble {
      width: 100% !important;
      height: auto !important;
      padding: 30px;
      background-color: $owned-media-white;
      border: 3px solid #7dc8b6;
      border-radius: 20px;
      box-shadow: 0 5px 20px rgb(0 0 0 / 10%);

      // スマホ版では背景画像を非表示
      &::before {
        display: none;
      }
    }

    &__reason-text {
      position: static !important; // スマホ版では通常のフローに戻す
      top: auto !important;
      left: auto !important;
      width: 100% !important;
      font-size: 20px;
      transform: none !important;
    }

    &__reason-weak {
      font-size: 18px;
    }
  }
}

// 成果の出るオウンドメディア運用セクション
.owned-media-success {
  @include owned-media-section;

  padding-top: 32px;
  background-image:
    linear-gradient(to right, rgb(97 106 109 / 15%) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(97 106 109 / 15%) 1px, transparent 1px);
  background-size: 48px 48px;

  &__container {
    @include owned-media-container;
  }

  // 見出しエリア
  &__header {
    position: relative;
    margin-bottom: 80px;
    text-align: center;
  }

  &__title-wrapper {
    position: relative;
    display: inline-block;

    &::before {
      position: absolute;
      top: 20%;
      left: 50%;
      z-index: 1;
      width: 110%;
      height: 100%;
      content: '';
      background-image: url('../images/s-owned/success-title-bg.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      transform: translateX(-50%);
    }
  }

  &__title {
    position: relative;
    z-index: 2;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    text-align: center;
  }

  &__title-main {
    display: block;
    font-size: clamp(100px, 10vw, 172px);
    font-weight: 900;
    line-height: 1.2;
    color: $owned-media-primary-color;
    letter-spacing: 2px;
  }

  &__title-sub {
    position: relative;
    display: block;
    padding-right: 48px;
    font-size: clamp(52px, 10vw, 92px);
    font-weight: 700;
    line-height: 1.2;
    color: $owned-media-text-color;
    letter-spacing: 1px;
  }

  &__title-mark {
    position: absolute;
    right: 0;
    bottom: 70px;
    font-size: clamp(100px, 10vw, 202px);
    font-weight: 700;
    color: $owned-media-text-color;
    transform: translate(50%, 50%) rotate(15deg);
  }

  // 説明テキスト
  &__description {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    margin-bottom: 100px;
    text-align: center;
  }

  &__description-text {
    position: relative;
    z-index: 1;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: clamp(24px, 10vw, 56px);
    font-weight: 700;
    line-height: 1.2;
    color: $owned-media-text-color;
    white-space: nowrap;

    &.marker {
      background-color: #fff54b;
    }
  }

  &__description-strong {
    font-size: clamp(28px, 10vw, 62px);
  }

  &__description-break {
    display: none; // PC版では改行しない
  }

  // 概念図エリア
  &__diagram {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}

// ////////////////////////////
// サービス内容
// ////////////////////////////
.owned-media-service {
  @include owned-media-section($owned-media-bg-color);

  background: linear-gradient(
    to bottom,
    $owned-media-base-color 0,
    $owned-media-base-color 700px,
    #d8ede8 700px,
    #d8ede8 100%
  );

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  // サービスリスト
  &__list {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 100px;
    width: 100%;
  }

  // サービスアイテム
  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 56px;
    padding: 120px 100px 60px; // 上部パディングを120px（60px + 60px）に調整
    background-color: $owned-media-white;
    border-radius: 34px;
    box-shadow: 0 0 14px rgb(67 226 184 / 60%);

    &:not(:last-child) {
      &::after {
        position: absolute;
        bottom: -68px;
        left: 50%;
        width: 140px;
        height: 50px;
        content: '';
        background-image: url('../images/s-owned/service-arrow.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        transform: translateX(-50%);
      }
    }
  }

  // 上段エリア（番号、タイトル、アイコン）（2段目）
  &__item-top {
    position: relative;
    display: flex;
    gap: 60px;
    align-items: center;
  }

  // 下段エリア（本文テキスト）（3段目）
  &__item-bottom {
    width: 100%;
  }

  // 番号エリア
  &__item-number {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 18px;
    align-items: center;

    // プログレスマークを擬似要素で表現
    &::before {
      position: absolute;
      top: -50px;
      left: 50%;
      width: 194px;
      height: 22px;
      content: '';
      background-image:
        // ドット7個（デフォルトはグレー）- 前面に表示
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        radial-gradient(circle, #d9d9d9 11px, transparent 11px),
        // 背景ライン - 後面に表示
        linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%);
      background-repeat: no-repeat;
      background-position:
        0 0,
        28px 0,
        57px 0,
        86px 0,
        115px 0,
        143px 0,
        172px 0,
        11px 10px;
      background-size:
        22px 22px,
        22px 22px,
        22px 22px,
        22px 22px,
        22px 22px,
        22px 22px,
        22px 22px,
        172px 3px;
      transform: translateX(-50%);
    }

    // プログレス状態はJavaScriptで動的に設定されます
  }

  &__item-number-main {
    font-family: 'SF Pro Text', helvetica, sans-serif;
    font-size: 100px;
    font-weight: 500;
    line-height: 1;
    color: #7dc8b6;
    letter-spacing: 1px;
  }

  &__item-number-sub {
    position: relative;
    margin-top: -25px;
    font-family: 'SF Pro Text', helvetica, sans-serif;
    font-size: 21px;
    font-weight: 500;
    line-height: 1;
    color: #7dc8b6;
    letter-spacing: 0.21px;

    &::before,
    &::after {
      display: inline-block;
      width: 15px;
      height: 4px;
      content: '';
      background-color: #3ab795;
      border-radius: 2px;
    }

    &::before {
      margin-right: 8px;
    }

    &::after {
      margin-left: 8px;
    }
  }

  // コンテンツエリア
  &__item-content {
    flex: 1;
    max-width: 715px;
  }

  &__item-title {
    font-family: $owned-media-font-family-noto;
    font-size: 45px;
    font-weight: 700;
  }

  &__item-title-normal {
    color: $owned-media-text-color;
    letter-spacing: 0.2px;
  }

  &__item-title-accent {
    font-size: 55px;
    color: $owned-media-primary-color;
    letter-spacing: 0.3px;
  }

  &__item-description {
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: 24px;
    font-weight: 400;
    line-height: 1.92;
    color: $owned-media-text-color;
    letter-spacing: 0.24px;
  }

  // アイコン
  &__item-icon {
    position: absolute;
    top: -160px;
    right: -60px;
    width: 365px;
    height: 250px;
    object-fit: contain;
  }

  // レスポンシブ対応
  @media (width <= 768px) {
    min-height: auto;

    &__header {
      margin-bottom: 60px;
    }

    &__title {
      font-size: 40px;
      letter-spacing: 0.4px;
    }

    &__subtitle-text {
      font-size: 20px;
      letter-spacing: 0.2px;
    }

    &__list {
      gap: 20px;
      padding: 0 20px;
    }

    &__item {
      gap: 30px;
      padding: 40px 20px; // モバイルではアイコンが通常フローなので通常のパディング
    }

    &__item-top {
      flex-direction: column;
      gap: 30px;
      align-items: center;
      margin-right: 0; // モバイルではマージンリセット
    }

    &__item-bottom {
      text-align: center;
    }

    &__item-number {
      flex-direction: row;
      gap: 5px;
      align-items: baseline;

      // モバイルではプログレスマークを非表示
      &::before {
        display: none;
      }
    }

    &__item-number-main {
      font-size: 32px;
      letter-spacing: 0.32px;
    }

    &__item-number-sub {
      margin-top: 0;
      font-size: 14px;
      letter-spacing: 0.14px;

      &::before,
      &::after {
        display: none;
      }
    }

    &__item-content {
      flex: none;
      max-width: none;
      padding-top: 0;
    }

    &__item-title {
      margin-bottom: 15px;
      font-size: 24px;
      line-height: 1.5;
    }

    &__item-title-normal {
      letter-spacing: 0;
    }

    &__item-title-accent {
      font-size: 28px;
      letter-spacing: 0;
    }

    &__item-description {
      font-size: 16px;
      line-height: 1.8;
      letter-spacing: 0.16px;
    }

    &__item-icon {
      position: static; // モバイルでは通常のフローに戻す
      width: 100%;
      max-width: 300px;
      height: auto;
      margin: 0 auto;
    }

    &__flow-bg {
      display: none;
    }
  }
}

// ////////////////////////////
// ご支援イメージ
// ////////////////////////////
.owned-media-support {
  @include owned-media-section($owned-media-bg-color);

  background: linear-gradient(
    to bottom,
    $owned-media-bg-color 0,
    $owned-media-bg-color 770px,
    $owned-media-mint-light 770px,
    $owned-media-mint-light 100%
  );

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  // カードコンテナ
  &__grid {
    display: grid;
    flex-wrap: wrap;
    grid-template-columns: repeat(2, 1fr);
    gap: 38px;
    margin: 0 auto;
  }

  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 18px;
    align-items: center;
    width: 634px;
    height: 679px;
    padding: 46px 8px 8px;
    overflow: hidden;
    background-color: $owned-media-white;
    border-radius: 43px;
    box-shadow: 0 0 14px rgb(0 0 0 / 25%);

    // タイトルエリア
    &-title-area {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 90%;
      height: 155px;
      background-color: #5f6061;
      border-radius: 13px;
    }

    &-title {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 34px;
      font-weight: 700;
      line-height: 54px;
      color: $owned-media-white;
      text-align: center;
      white-space: nowrap;
    }

    // 画像エリア
    &-images {
      position: relative;
      bottom: 0;
      box-sizing: border-box;
      display: flex;
      gap: 15px;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 336px;
      min-height: 336px;
      padding: 27px;
      background-color: #e1ede8;
      border-radius: 10px 10px 36px 36px;
    }

    &-image {
      overflow: hidden;
      border-radius: 14px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &--wide {
        width: 100%;
        max-width: none;
      }
    }

    &-content {
      width: 540px;
      text-align: center;
    }

    &-text {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 24px;
      line-height: 44px;
      color: #333;
      text-align: center;
      letter-spacing: 0.24px;
    }

    &-normal {
      font-weight: 500;
      letter-spacing: 0.06px;
    }

    &-accent {
      font-size: 28px;
      font-weight: 700;
      letter-spacing: 0.08px;
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    padding: 80px 0 60px;

    &::before {
      height: calc(100% - 200px);
    }

    &__title {
      font-size: 48px;
    }

    &__subtitle-text {
      font-size: 24px;
    }

    &__grid {
      flex-direction: column;
      gap: 40px;
      align-items: center;
    }

    &__item {
      width: 100%;
      max-width: 350px;
      height: auto;
      min-height: 400px;

      &-title-area {
        position: relative;
        top: 20px;
        left: 20px;
        width: calc(100% - 40px);
        height: 80px;
      }

      &-title {
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0.8px;
      }

      &-images {
        position: relative;
        bottom: auto;
        left: auto;
        width: calc(100% - 20px);
        height: 200px;
        margin: 20px 10px 10px;
      }

      &-content {
        position: relative;
        bottom: auto;
        left: auto;
        width: calc(100% - 40px);
        margin: 10px 20px 20px;
      }

      &-text {
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
}

// ////////////////////////////
// メリット
// ////////////////////////////
.owned-media-merit {
  @include owned-media-section($owned-media-base-color);

  &__container {
    @include owned-media-container;
  }

  // 背景装飾
  &__decorations {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  &__decoration {
    position: absolute;
    width: 410px;
    height: 413px;

    &--1 {
      top: 531px;
      left: 273px;
    }

    &--2 {
      top: 1195px;
      right: 297px;
    }

    &--3 {
      top: 1910px;
      left: 273px;
    }

    &--4 {
      top: 2555px;
      right: 297px;
    }

    &--5 {
      top: 3270px;
      left: 273px;
    }
  }

  // 見出し
  &__header {
    position: relative;
    z-index: 2;
    margin-bottom: 120px;
    text-align: center;
  }

  &__brand {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;

    &-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: fit-content;
      height: 88px;
      padding: 12px 24px;
      content: '';
      background-color: $owned-media-mint;
      border-radius: 44px;

      &::after {
        position: absolute;
        bottom: -16px;
        left: 50%;
        display: block;
        width: 32px;
        height: 32px;
        content: '';
        background-color: $owned-media-mint;
        transform: translateX(-50%) rotate(45deg);
      }
    }

    &-name {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 32px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-white;
      letter-spacing: 0.32px;
    }

    &-suffix {
      font-size: 28px;
      letter-spacing: 0.08px;
    }

    &-service {
      position: absolute;
      top: 4px;
      left: 169px;
      font-family: $owned-media-font-family-noto;
      font-size: 28px;
      font-weight: 700;
      color: $owned-media-white;
      letter-spacing: 0.28px;
      white-space: nowrap;
    }
  }

  &__catch {
    &-line1 {
      display: flex;
      gap: 20px;
      align-items: flex-end;
      justify-content: center;
      margin-bottom: 18px;
    }

    &-text {
      font-family: $owned-media-font-family-noto;
      font-size: 55px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-text-color;
      letter-spacing: 0.55px;
    }

    &-wo {
      font-family: $owned-media-font-family-noto;
      font-size: 42px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-text-color;
      letter-spacing: 0.42px;
    }

    &-save {
      font-family: $owned-media-font-family-noto;
      font-size: 83px;
      font-weight: 900;
      line-height: 1.2;
      color: $owned-media-mint;
      letter-spacing: 0.83px;
    }
  }

  &__title {
    display: flex;
    align-items: flex-end;
    justify-content: center;

    &-number {
      font-family: 'SF Pro', $owned-media-font-family-noto;
      font-size: 160px;
      font-weight: 590;
      text-align: center;
      background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &-text {
      display: flex;
      gap: 10px;
      align-items: baseline;
    }

    &-suffix {
      font-family: $owned-media-font-family-noto;
      font-size: 86px;
      font-weight: 500;
      line-height: 1.2;
      color: $owned-media-text-color;
      letter-spacing: 0.86px;
    }

    &-main {
      font-family: $owned-media-font-family-noto;
      font-size: 86px;
      font-weight: 500;
      line-height: 1.2;
      color: $owned-media-text-color;
    }

    &-accent {
      font-family: $owned-media-font-family-noto;
      font-size: 106px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-mint;
      letter-spacing: -12px;
    }
  }

  // メリットリスト
  &__list {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 120px;
  }

  &__item {
    position: relative;
    width: 100%;

    &-content {
      display: flex;
      gap: 24px;
      align-items: center;
      justify-content: space-between;
    }

    &-image {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 567px;
      height: 567px;
      background-image: url('../images/s-owned/subtract-9.svg');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &-img {
      width: 487px;
      height: auto;
      border-radius: 25px;
      box-shadow: 0 0 24px rgb(90 134 151 / 50%);
    }

    &-text-area {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: flex-start;
      max-width: 700px;
    }

    &-header {
      margin-bottom: 50px;
    }

    &-number {
      position: relative;
      display: flex;
      gap: 12px;
      align-items: flex-end;
      justify-content: flex-start;

      &-text {
        font-family: 'SF Pro Text', $owned-media-font-family-noto;
        font-size: 160px;
        font-weight: 300;
        color: $owned-media-mint;
        letter-spacing: 1.6px;
      }

      &-icon {
        position: relative;
        width: 128px;
        height: 127px;
      }

      &-bg {
        position: absolute;
        top: 0;
        left: 3px;
        width: 118px;
        height: 127px;
      }

      &-label {
        position: absolute;
        top: 37px;
        left: 0;
        width: 100%;
        font-family: 'SF Pro Text', $owned-media-font-family-noto;
        font-size: 49px;
        font-weight: 500;
        line-height: 1;
        text-align: center;
        letter-spacing: 0.49px;
        background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    &-text {
      width: 100%;
    }

    &-title {
      margin: 0 0 30px;
      font-family: $owned-media-font-family-noto;
      font-size: 45px;
      font-weight: 700;
      line-height: 1.5;

      &-main {
        font-size: 55px;
        color: $owned-media-primary-color;
      }

      &-sub {
        font-size: 45px;
        color: $owned-media-text-color;
      }

      &-accent {
        font-size: 45px;
        font-weight: 900;
        color: $owned-media-text-color;
      }
    }

    &-desc {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 24px;
      font-weight: 400;
      line-height: 1.9;
      color: $owned-media-text-color;
      text-shadow: 0 0 9px $owned-media-white;
      letter-spacing: 0.24px;
    }

    // 偶数アイテムは左右反転
    &--02,
    &--04 {
      .owned-media-merit__item-content {
        flex-direction: row-reverse;
      }
    }
  }

  // レスポンシブ対応
  @media (width <= 768px) {
    min-height: auto;
    padding: 80px 0 60px;

    &__decoration {
      display: none;
    }

    &__header {
      margin-bottom: 60px;
    }

    &__brand {
      margin-bottom: 60px;

      &-text {
        font-size: 20px;
      }

      &-suffix {
        font-size: 18px;
      }

      &-service {
        position: static;
        margin-top: 5px;
        font-size: 18px;
      }
    }

    &__catch {
      margin-bottom: 50px;

      &-line1 {
        flex-direction: column;
        gap: 10px;
      }

      &-text {
        font-size: 28px;
      }

      &-divider {
        width: 100px;
        height: 3px;
      }

      &-wo {
        font-size: 24px;
      }

      &-save {
        font-size: 42px;
      }
    }

    &__title {
      flex-direction: column;
      gap: 10px;

      &-number {
        font-size: 80px;
      }

      &-text {
        gap: 5px;
      }

      &-suffix,
      &-main {
        font-size: 42px;
      }

      &-accent {
        font-size: 52px;
      }
    }

    &__list {
      gap: 60px;
    }

    &__item {
      &-content {
        flex-direction: column;
        gap: 30px;
        min-height: auto;
      }

      &-image {
        width: 100%;
        max-width: 350px;
        height: 350px;
        margin: 0 auto;
      }

      &-img {
        width: 300px;
      }

      &-text-area {
        width: 100%;
        max-width: none;
      }

      &-number {
        justify-content: center;

        &-text {
          font-size: 80px;
        }

        &-icon {
          width: 80px;
          height: 80px;
        }

        &-bg {
          width: 74px;
          height: 80px;
        }

        &-label {
          top: 20px;
          font-size: 24px;
        }
      }

      &-text {
        text-align: center;
      }

      &-title {
        font-size: 24px;

        &-main {
          font-size: 28px;
        }

        &-accent {
          font-size: 28px;
        }
      }

      &-desc {
        font-size: 16px;
        line-height: 1.7;
      }

      // モバイルでは全て同じレイアウト
      &--02,
      &--04 {
        .owned-media-merit__item-content {
          flex-direction: column;
        }
      }
    }
  }
}

// ////////////////////////////
// ご支援体制
// ////////////////////////////
.owned-media-system-support {
  @include owned-media-section($owned-media-bg-color);

  background: linear-gradient(
    to bottom,
    $owned-media-bg-color 0,
    $owned-media-bg-color 770px,
    $owned-media-mint-light 770px,
    $owned-media-mint-light 100%
  );

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  &__visual {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    .visual-container {
      display: flex;
      align-items: stretch;
      width: 100%;
      height: 100%;
    }

    .visual-left {
      display: flex;
      flex-direction: column;
      gap: 32px;
      align-items: center;
      justify-content: space-between;
      width: 70%;
      min-height: 300px;

      &__text {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: fit-content;
        height: 96px;
        padding: 34px 56px;
        font-size: 45px;
        font-weight: 700;
        color: #fff;
        background-color: #7dc8b6;
        border-radius: 48px;
      }

      &__image {
        position: relative;
        display: flex;
        flex: 1;
        gap: 20px;
        align-items: center;
        justify-content: center;
        width: fit-content;
        height: fit-content;
        padding: 100px 48px;
        background-color: #fff;
        border-radius: 18px;
        box-shadow: 0 0 35px 0 rgb(58 183 149 / 60%);

        &::after {
          position: absolute;
          top: 30%;
          right: -290px;
          width: 230px;
          height: 140px;
          content: '';
          background-image: url('#{$owned-media-image-path}/support-arrow.png');
          background-repeat: no-repeat;
          background-size: contain;
          transform: translateX(-50%);
        }

        img {
          flex-shrink: 0;
          object-fit: cover;

          &:first-child {
            width: 382px;
            height: 376px;
          }

          &:last-child {
            width: 233px;
            height: 279px;
          }
        }
      }
    }

    .visual-right {
      display: flex;
      flex-direction: column;
      gap: 32px;
      align-items: center;
      justify-content: space-between;
      width: 30%;
      min-height: 300px;

      &__text {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: fit-content;
        height: 96px;
        padding: 34px 56px;
        font-size: 45px;
        font-weight: 700;
        color: #fff;
        background-color: #7dc8b6;
        border-radius: 48px;
      }

      &__image {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        width: fit-content;
        height: fit-content;
        padding: 100px 48px;
        background-color: #fff;
        border-radius: 18px;
        box-shadow: 0 0 35px 0 rgb(58 183 149 / 60%);

        img {
          flex-shrink: 0;
          width: 233px;
          height: 279px;
          object-fit: cover;
        }
      }
    }
  }

  &__team-image {
    width: 1370px;
    height: 712px;
    object-fit: contain;
  }

  &__message {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin-top: 64px;
  }

  &__message-bubble {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    height: 140px;
    padding: 56px;
    background-color: #fff;
    border-radius: 70px;
    box-shadow: 0 0 35px 0 rgb(58 183 149 / 60%);

    &::before {
      position: absolute;
      top: -35px;
      left: 65%;
      z-index: 0;
      width: 70px;
      height: 70px;
      content: '';
      background-color: #fff;
      transform: rotate(45deg);
    }
  }

  &__message-accent {
    position: relative;
    z-index: 2;
    font-size: 42px;
    font-weight: 900;
    color: #fa6b58;
    letter-spacing: 0.18px;
    -webkit-text-stroke: 1px #fa6b58;

    &::before {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 12px;
      content: '';
      background-color: #fff54b;
    }
  }

  &__message-text {
    font-size: 42px;
    font-weight: 900;
    color: #333;
    letter-spacing: 0.18px;
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    min-height: 1725px;
    padding: 80px 0 60px;

    &__header {
      margin-bottom: 126px;
    }

    &__title {
      width: 198px;
      font-size: 28px;
      letter-spacing: 0.28px;
    }

    &__subtitle {
      gap: 6px;

      &-line {
        width: 15px;
        height: 2px;
      }

      &-text {
        font-size: 20px;
        letter-spacing: 0.2px;
      }
    }

    &__content {
      height: 1315px;
    }

    &__visual {
      top: 410px;
      height: 1315px;
      background-color: #b1e2d5;
    }

    &__team-image {
      position: absolute;
      top: 261px;
      left: 5px;
      width: 365px;
      height: 807px;
    }

    &__message {
      top: 1071px;
      left: 50%;
      width: 375px;
      height: 234px;
      transform: translateX(-50%);
    }

    &__message-bubble {
      background-image: url('#{$owned-media-image-path}/union-1.svg');
    }

    &__message-highlight {
      &::before {
        bottom: 19px;
        left: 16px;
        width: 245px;
        height: 28px;
      }
    }

    &__message-accent,
    &__message-text {
      font-size: 24px;
      line-height: 35px;
      letter-spacing: 0.24px;
    }

    &__message-accent {
      font-weight: 900;
      letter-spacing: 0.06px;
    }

    &__message-text {
      font-weight: 500;
      letter-spacing: 0.06px;
    }
  }
}

// ////////////////////////////
// ご支援事例
// ////////////////////////////
.owned-media-case-study {
  @include owned-media-section($owned-media-bg-color);

  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 850px, #b1e2d5 850px, #b1e2d5 100%);

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  &__content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  &__service-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  &__service-title {
    position: relative;
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    width: fit-content;
    height: 67px;
    padding: 20px 48px;
    margin: 0 auto 80px;
    background-color: $owned-media-primary-color;
  }

  &__service-category,
  &__service-name {
    font-family: $owned-media-font-family-noto;
    font-size: 45px;
    font-weight: 700;
    line-height: 28px;
    color: #fff;
    letter-spacing: 0.45px;
    white-space: nowrap;
  }

  &__service-description {
    margin-bottom: 32px;
    font-family: $owned-media-font-family-noto;
    font-size: 42px;
    font-weight: 500;
    line-height: 28px;
    color: #191919;
    text-align: center;
    letter-spacing: 0.42px;
  }

  &__service-period {
    font-family: $owned-media-font-family-noto;
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
    color: #989898;
    text-align: center;
    letter-spacing: 0.24px;
  }

  &__results-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 90px;
    margin-top: 48px;
    background-color: #fff;
    border: 6px solid #3c8b86;
    border-radius: 20px;
    box-shadow: 0 0 35px 0 rgb(60 139 134 / 100%);

    .result-inner {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;

      &__main {
        font-size: 46px;
        font-weight: 500;
        color: #191919;
        white-space: nowrap;
      }

      &__accent {
        font-size: 46px;
        font-weight: 700;
        color: #191919;
        white-space: nowrap;
      }

      &__strong {
        font-size: 74px;
        font-weight: 900;
        color: #fa6b58;
        white-space: nowrap;
      }
    }

    .result-graph {
      width: 100%;
      margin-top: 48px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
  }
}

// ////////////////////////////
// フロー
// ////////////////////////////
.owned-media-contract-flow {
  @include owned-media-section($owned-media-bg-color);

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  &__content {
    display: flex;
    flex-direction: column;
    gap: 72px;
    align-items: center;
    width: 100%;
  }

  .flow-step {
    position: relative;
    display: flex;
    flex: 1;
    gap: 32px;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 254px;
    padding: 48px 48px 48px 0;
    background-color: #fff;
    filter: drop-shadow(5px 5px 8px rgb(0 0 0 / 30%));
    border-radius: 20px;

    &::after {
      position: absolute;
      bottom: -28px;
      left: 48%;
      width: 56px;
      height: 56px;
      content: '';
      background-color: #fff;
      transform: rotate(135deg) skew(20deg, 20deg);
    }

    &__number {
      flex-shrink: 0;
      padding: 18px 48px 18px 32px;
      font-family: $owned-media-font-family-yu;
      font-size: 92px;
      font-size: clamp(40px, 92px, 92px);
      font-weight: 700;
      color: #fff;
      text-align: center;
      white-space: nowrap;
      background-color: #7fd5bf;
      border-radius: 0 75px 75px 0;
    }

    &__description {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 24px;
      color: $owned-media-text-color-sumikuro;

      &-title {
        font-size: 54px;
        font-size: clamp(40px, 54px, 54px);
        font-weight: 700;
        line-height: normal;
      }

      &-text {
        font-family: $owned-media-font-family-noto;
        font-size: 28px;
        font-weight: 400;
        line-height: normal;
      }
    }

    &__icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 194px;
      height: auto;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    min-height: 2500px;
    padding: 80px 0 60px;

    &__header {
      margin-bottom: 60px;
    }

    &__content {
      height: 2000px;
    }

    &__step {
      width: 350px;
      height: 200px;
      margin-bottom: 80px;

      &--01 {
        top: 0;
      }

      &--02 {
        top: 280px;
      }

      &--03 {
        top: 560px;
      }

      &--04 {
        top: 840px;
      }

      &--05 {
        top: 1120px;
        height: 180px;
      }
    }

    &__step-number {
      top: 60px;
      left: 20px;
      width: 60px;
      height: 20px;
      font-size: 32px;
      line-height: 20px;
      letter-spacing: 0.32px;
    }

    &__step-title {
      top: 50px;
      left: 100px;
      font-size: 28px;
      line-height: 20px;
      letter-spacing: 0.28px;
    }

    &__step-description {
      top: 100px;
      left: 20px;
      max-width: 310px;
      font-size: 18px;
      line-height: 22px;
      letter-spacing: 0.18px;
    }

    &__step-icon {
      top: 10px;
      right: 10px;
      width: 100px;
      height: 100px;
    }
  }
}

// ////////////////////////////
// よくある質問 (FAQ)
// ////////////////////////////

.owned-media-faq {
  @include owned-media-section;

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  .faq {
    &__list {
      display: flex;
      flex-direction: column;
    }

    &__item {
      display: flex;
      flex-direction: column;
      gap: 60px;
      padding: 40px 0;
      border-bottom: 1px solid #a6bfc3;

      .faq-text {
        display: flex;
        gap: 20px;
        align-items: center;
        font-family: $owned-media-font-family-noto;

        &-wrap {
          display: flex;
          gap: 20px;
          align-items: center;

          &.question {
          }

          &.answer {
            padding-left: 72px;
          }
        }

        &::before {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          width: 46px;
          height: 46px;
          font-size: 28px;
          font-weight: 900;
          line-height: 1;
          color: #fff;
          border-radius: 7px;
        }

        &.question {
          &::before {
            content: 'Q';
            background-color: $owned-media-mint;
          }
        }

        &.answer {
          &::before {
            content: 'A';
            background-color: #a6bfc3;
          }
        }

        &-text {
          font-size: 38px;
          font-weight: 700;
          line-height: normal;
          color: $owned-media-text-color-sumikuro;

          &.question {
            color: $owned-media-text-color-sumikuro;
          }

          &.answer {
            font-size: 24px;
            font-weight: 400;
            line-height: 1.5;
            color: $owned-media-text-color;
          }
        }
      }
    }
  }

  @include owned-media-mobile {
    padding: 60px 0;

    &__header {
      margin-bottom: 60px;
    }

    &__title {
      margin-bottom: 30px;
      font-size: 48px;
    }

    &__title-group {
      gap: 15px;
    }

    &__title-en {
      font-size: 24px;
    }

    &__decoration-line {
      width: 12px;
      height: 3px;
    }

    &__item {
      padding: 30px 0;
    }

    &__question {
      gap: 15px;
      margin-bottom: 30px;
    }

    &__q-label,
    &__a-label {
      width: 36px;
      height: 36px;

      span {
        font-size: 20px;
      }
    }

    &__q-text {
      padding-top: 8px;
      font-size: 24px;
    }

    &__answer {
      gap: 15px;
      padding-left: 18px;
    }

    &__a-text {
      padding-top: 8px;
      font-size: 18px;
      line-height: 32px;
      letter-spacing: 0.36px;
    }
  }
}

.owned-media-discovery__title-suffix,
.owned-media-discovery__title-prefix {
  &--small {
    font-size: 48px !important;
  }
}
