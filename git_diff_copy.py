#!/usr/bin/env python3
"""
gitリポジトリの差分ファイルをコピーする
./git_diff_copy-destに保存
"""

import os
import subprocess
import shutil

# 対象のディレクトリを指定
TARGET_DIR = "/home/<USER>/projects/wordpress-appmart"
# 出力先ディレクトリを固定
DEST_DIR = "./change_data"


def run_git_command(args, cwd):
    """
    Gitコマンドを実行し、結果を返す

    Args:
        args (list): Gitコマンドの引数リスト
        cwd (str): コマンドを実行するディレクトリ

    Returns:
        str: コマンドの標準出力
    """
    print(f"Running command: {' '.join(args)} in {cwd}")  # デバッグメッセージ
    result = subprocess.run(
        args, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, cwd=cwd
    )
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        raise subprocess.CalledProcessError(result.returncode, args)
    return result.stdout.strip()


def get_latest_commit_sha():
    """
    現在のブランチの最新コミットのSHAを取得します。

    Returns:
        str: 最新コミットのSHA
    """
    return run_git_command(["git", "rev-parse", "HEAD"], TARGET_DIR)


def get_diff_files(new_sha, old_sha):
    """
    2つのgitコミット間で異なるファイルのリストを取得します。

    Args:
        new_sha (str): 新しいコミットのSHA
        old_sha (str): 古いコミットのSHA

    Returns:
        list: 異なるファイルのリスト
    """
    output = run_git_command(
        ["git", "diff", "--name-only", old_sha, new_sha],
        TARGET_DIR,
    )
    print(f"Diff files: {output}")  # デバッグメッセージ
    return output.splitlines()


def get_deleted_and_renamed_files(new_sha, old_sha):
    """
     2つのgitコミット間で削除されたファイルと名前変更されたファイルのリストを取得します。

    Args:
        new_sha (str): 新しいコミットのSHA
        old_sha (str): 古いコミットのSHA

    Returns:
        list: 削除されたファイルと名前変更されたファイルのリスト
    """
    output = run_git_command(
        ["git", "diff", "--name-status", "--diff-filter=DR", f"{old_sha}..{new_sha}"],
        TARGET_DIR,
    )
    return output.splitlines()


def copy_files(files, destination, exclude_files=None):
    """
    指定されたファイルのリストを出力先ディレクトリにコピーし、
    ディレクトリ構造を保持します。

    Args:
        files (list): コピーするファイルのリスト
        destination (str): 出力先ディレクトリのパス
        exclude_files (set): 除外するファイルのセット
    """
    exclude_files = exclude_files or set()  # 除外リストを初期化
    for file in files:
        if file in exclude_files:  # 除外リストに含まれている場合はスキップ
            continue
        file_path = os.path.join(TARGET_DIR, file)
        if os.path.exists(file_path):
            dest_path = os.path.join(destination, file)
            os.makedirs(os.path.dirname(dest_path), exist_ok=True)
            shutil.copy2(file_path, dest_path)
        else:
            print(f"Warning: {file} does not exist and will be skipped.")


def document():
    # 現在のブランチの最新のコミットSHAを取得
    new_sha = get_latest_commit_sha()
    print(f"Latest commit SHA: {new_sha}")

    old_sha = input("Enter the SHA of the older commit: ")

    # 出力先ディレクトリを削除してから再度作成
    if os.path.exists(DEST_DIR):
        shutil.rmtree(DEST_DIR)
    os.makedirs(DEST_DIR)

    # 差分ファイルを取得
    diff_files = get_diff_files(new_sha, old_sha)
    print(f"Files to copy: {diff_files}")  # デバッグメッセージ

    # 除外するファイルを指定
    exclude_files = {".env"}
    copy_files(diff_files, DEST_DIR, exclude_files)
    print(f"Copied {len(diff_files)} files to {DEST_DIR}")

    # 削除されたファイルのリストを取得してテキストファイルに出力
    deleted_files = get_deleted_and_renamed_files(new_sha, old_sha)
    with open(os.path.join(DEST_DIR, "deleted_files.txt"), "w") as f:
        for line in deleted_files:
            # タブで分割し、最初の要素（ステータス）と最後の要素（ファイル名）を取得
            parts = line.split("\t")
            if parts:  # 空行でないことを確認
                status = parts[0]
                file = parts[-1]  # 最後の要素をファイル名として使用
                if status == "D":
                    f.write(f"{file}\n")
    print(f"Deleted files list saved to {os.path.join(DEST_DIR, 'deleted_files.txt')}")


if __name__ == "__main__":
    document()
